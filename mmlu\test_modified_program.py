#!/usr/bin/env python3
"""
测试修改后的程序结构（不调用OpenAI API）
"""

import json
import os

# 定义三个固定的persona
PERSONAS = [
    {
        "name": "Analytical Thinker",
        "prompt": "You are an analytical thinker who approaches problems systematically. You break down complex questions into smaller parts, analyze each component carefully, and use logical reasoning to arrive at conclusions. Always explain your step-by-step thinking process."
    },
    {
        "name": "Creative Problem Solver", 
        "prompt": "You are a creative problem solver who thinks outside the box. You look for patterns, make connections between seemingly unrelated concepts, and often find innovative approaches to problems. You consider multiple perspectives before reaching a conclusion."
    },
    {
        "name": "Detail-Oriented Validator",
        "prompt": "You are a detail-oriented validator who focuses on accuracy and precision. You carefully check facts, verify calculations, and look for potential errors or oversights. You are methodical and thorough in your analysis."
    }
]

def load_jsonl_data_by_subject(data_dir):
    """按学科分别加载所有jsonl格式的MMLU数据"""
    subjects_data = {}
    for subject_dir in os.listdir(data_dir):
        subject_path = os.path.join(data_dir, subject_dir)
        if os.path.isdir(subject_path):
            jsonl_files = [f for f in os.listdir(subject_path) if f.endswith('.jsonl')]
            for jsonl_file in jsonl_files:
                file_path = os.path.join(subject_path, jsonl_file)
                subject_name = subject_dir
                if subject_name not in subjects_data:
                    subjects_data[subject_name] = []
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        data = json.loads(line.strip())
                        subjects_data[subject_name].append(data)
    return subjects_data

def parse_question_answer(data_item):
    """从jsonl数据项中解析问题和答案"""
    question = data_item["question"]
    choices = data_item["choices"]
    
    # 格式化问题
    formatted_question = "Can you answer the following question as accurately as possible? {}: A) {}, B) {}, C) {}, D) {} Explain your answer, putting the answer in the form (X) at the end of your response.".format(
        question, choices[0], choices[1], choices[2], choices[3]
    )
    
    # 答案索引转换为字母
    answer_idx = data_item["answer"]
    answer_letter = chr(ord('A') + answer_idx)
    
    return formatted_question, answer_letter

def create_agent_context(persona, question):
    """为特定persona创建初始对话上下文"""
    return [
        {"role": "system", "content": persona["prompt"]},
        {"role": "user", "content": question}
    ]

if __name__ == "__main__":
    # 固定参数
    agents = 3
    rounds = 1  # 只辩论一轮

    # 加载MMLU数据（按学科分类）
    data_dir = "."  # 当前目录下的各个学科文件夹
    subjects_data = load_jsonl_data_by_subject(data_dir)

    print(f"Loaded {len(subjects_data)} subjects:")
    total_questions = 0
    for subject, data in subjects_data.items():
        print(f"  {subject}: {len(data)} questions")
        total_questions += len(data)
    
    print(f"\nTotal questions across all subjects: {total_questions}")

    # 测试每个学科的第一个问题的处理流程
    for subject_name, subject_data in subjects_data.items():
        print(f"\n{'='*60}")
        print(f"Testing subject: {subject_name.upper()}")
        print(f"Total questions: {len(subject_data)}")
        print(f"{'='*60}")
        
        if subject_data:
            # 只测试第一个问题
            data_item = subject_data[0]
            question, answer = parse_question_answer(data_item)

            print(f"\nSample question from {subject_name}:")
            print(f"Original: {data_item['question']}")
            print(f"Choices: {data_item['choices']}")
            print(f"Correct answer index: {data_item['answer']}")
            print(f"Correct answer letter: {answer}")
            
            # 测试agent上下文创建
            print(f"\nAgent contexts for this question:")
            for j in range(agents):
                context = create_agent_context(PERSONAS[j], question)
                print(f"\nAgent {j+1} ({PERSONAS[j]['name']}):")
                print(f"  System prompt: {context[0]['content'][:100]}...")
                print(f"  Question: {context[1]['content'][:100]}...")
            
            print(f"\nThis subject would process {len(subject_data)} questions in total.")
            
            # 模拟输出文件名
            output_filename = f"mmlu_personas_{subject_name}_{agents}_{rounds}.json"
            print(f"Results would be saved to: {output_filename}")
        
        break  # 只测试第一个学科

    print(f"\n{'='*60}")
    print("STRUCTURE TEST COMPLETED!")
    print(f"The program is ready to process all {total_questions} questions across {len(subjects_data)} subjects.")
    print(f"{'='*60}")
