#!/usr/bin/env python3
"""
测试数据加载功能
"""

import json
import os

def load_jsonl_data(data_dir):
    """加载所有jsonl格式的MMLU数据"""
    all_data = []
    for subject_dir in os.listdir(data_dir):
        subject_path = os.path.join(data_dir, subject_dir)
        if os.path.isdir(subject_path):
            jsonl_files = [f for f in os.listdir(subject_path) if f.endswith('.jsonl')]
            for jsonl_file in jsonl_files:
                file_path = os.path.join(subject_path, jsonl_file)
                print(f"Loading: {file_path}")
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        data = json.loads(line.strip())
                        all_data.append(data)
    return all_data

def parse_question_answer(data_item):
    """从jsonl数据项中解析问题和答案"""
    question = data_item["question"]
    choices = data_item["choices"]
    
    # 格式化问题
    formatted_question = "Can you answer the following question as accurately as possible? {}: A) {}, B) {}, C) {}, D) {} Explain your answer, putting the answer in the form (X) at the end of your response.".format(
        question, choices[0], choices[1], choices[2], choices[3]
    )
    
    # 答案索引转换为字母
    answer_idx = data_item["answer"]
    answer_letter = chr(ord('A') + answer_idx)
    
    return formatted_question, answer_letter

if __name__ == "__main__":
    # 测试数据加载
    data_dir = "."
    print("Loading MMLU data...")
    all_data = load_jsonl_data(data_dir)
    
    print(f"Total questions loaded: {len(all_data)}")
    
    if all_data:
        # 显示第一个问题的示例
        sample_data = all_data[0]
        print("\nSample data item:")
        print(f"Subject: {sample_data.get('subject', 'Unknown')}")
        print(f"Question: {sample_data['question']}")
        print(f"Choices: {sample_data['choices']}")
        print(f"Answer index: {sample_data['answer']}")
        
        # 测试解析功能
        formatted_question, answer_letter = parse_question_answer(sample_data)
        print(f"\nFormatted question: {formatted_question}")
        print(f"Answer letter: {answer_letter}")
        
        # 统计各学科的问题数量
        subjects = {}
        for item in all_data:
            subject = item.get('subject', 'Unknown')
            subjects[subject] = subjects.get(subject, 0) + 1
        
        print(f"\nQuestions by subject:")
        for subject, count in subjects.items():
            print(f"  {subject}: {count}")
    else:
        print("No data loaded. Please check the directory structure.")
